{"name": "mergit-customer", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --dev-client", "ios": "expo run:ios", "android": "expo run:android", "build:dev": "eas build --profile development", "build:preview": "eas build --profile preview", "build:prod": "eas build --profile production", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "@gorhom/bottom-sheet": "^5.2.6", "@hookform/resolvers": "^5.2.2", "@react-navigation/native": "^7.0.3", "@sentry/react-native": "~6.20.0", "@tanstack/react-query": "^5.90.1", "axios": "^1.12.2", "expo": "^54.0.10", "expo-build-properties": "~1.0.9", "expo-constants": "~18.0.9", "expo-contacts": "~15.0.8", "expo-dev-client": "~6.0.12", "expo-font": "~14.0.8", "expo-haptics": "~15.0.7", "expo-image": "~3.0.8", "expo-image-picker": "~17.0.8", "expo-linear-gradient": "~15.0.7", "expo-linking": "~8.0.8", "expo-localization": "~17.0.7", "expo-location": "~19.0.7", "expo-router": "~6.0.8", "expo-status-bar": "~3.0.8", "expo-system-ui": "~6.0.7", "expo-web-browser": "~15.0.7", "i18next": "^25.5.2", "lottie-react-native": "~7.3.1", "nativewind": "latest", "onesignal-expo-plugin": "^2.0.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.63.0", "react-i18next": "^15.7.3", "react-native": "0.81.4", "react-native-gesture-handler": "~2.28.0", "react-native-keyboard-controller": "1.18.5", "react-native-maps": "1.20.1", "react-native-mmkv": "^3.3.3", "react-native-onesignal": "^5.2.13", "react-native-otp-entry": "^1.8.5", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1", "react-native-svg-transformer": "^1.5.1", "react-native-web": "^0.21.0", "socket.io-client": "^4.8.1", "yup": "^1.7.1", "zustand": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.1.10", "ajv": "^8.12.0", "eslint": "^9.25.1", "eslint-config-expo": "~10.0.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.0", "typescript": "~5.9.2"}, "private": true}