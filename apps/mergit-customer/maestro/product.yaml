appId: 'com.mergit.customer'
---

- scrollUntilVisible:
      element:
          text: 'Get farm-fresh groceries & daily essentials'
      direction: DOWN
      timeout: 8000
      speed: 80
      centerElement: true
- assertVisible: 'Product'
- assertVisible:
      id: 'productEntry'
- tapOn:
      id: 'productEntry'
- assertVisible:
      id: 'searchClick'
- tapOn:
      id: 'searchClick'
- inputText: 'SkinCare'
- tapOn: 'search'
- assertVisible:
      id: 'search_result_product_1'
- tapOn:
      id: 'search_result_product_1'
- assertVisible:
      text: 'Items'
- tapOn: 'Items'
- assertVisible:
      text: 'Shops'
- tapOn: 'Shops'
- assertVisible:
      id: 'sortPress'
- tapOn:
      id: 'sortPress'
- tapOn: 'Price (High to Low)'
- tapOn: 'Price (Low to High)'
- tapOn: 'Distance (high to low)'
- tapOn:
      id: 'cancelModal'
- tapOn: 'Pure Veg'
- tapOn: 'Rated 4+'
- tapOn:
      id: 'modalBack'
- tapOn:
      id: 'modalBack'
- tapOn:
      text: 'Home'
