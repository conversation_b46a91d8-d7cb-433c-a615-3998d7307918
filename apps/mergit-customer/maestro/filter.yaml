appId: 'com.mergit.customer'
---

- assertVisible:
      id: 'cusinesClick'
- tapOn:
      id: 'cusinesClick'
- assertVisible:
      id: 'filterPress'
- tapOn:
      id: 'filterPress'
- tapOn:
      id: 'filterSearch'
- inputText: 'Indian'
- eraseText
- tapOn: 'go'
- tapOn:
      text: 'Indian'
- tapOn:
      text: 'Italian'
- tapOn:
      text: 'Thai'            
- assertVisible:
      text: 'Cuisines'
- assertVisible:
      text: 'Ratings'
- tapOn:
      text: 'Ratings'
- assertVisible:
      text: 'Veg / Non Veg'
- tapOn:
      text: 'Veg / Non Veg'
- tapOn:
      text: 'Non-Veg'
- tapOn:
      text: 'Pure Veg'      
- tapOn:
      text: 'Veg & Egg'    
- tapOn:
      text: 'Sort'
- tapOn:
      text: 'Price (High to Low)'  
- tapOn:
      text: 'Price (Low to High)'
- tapOn:
      text: 'Distance (high to low)' 
- tapOn:
      text: 'Apply'
- tapOn:
      id: 'closeModal'                                     
- tapOn:
      id: 'hotelClick'      