appId: 'com.mergit.customer'
---
- scrollUntilVisible:
      element:
          id: 'scroll_offerzone'
      direction: DOWN
      timeout: 8000
      speed: 20
      centerElement: true
- assertVisible:
      id: 'offerZoneCard'
- tapOn:
      id: 'offerZoneCard'
- assertVisible:
      text: 'Top Offers for you'
- assertVisible:
      text: 'FLAT 125 OFF'
- assertVisible:
      text: 'BUY 1 GET 1'
- tapOn:
      text: 'FLAT 125 OFF'
- tapOn:
      text: 'BUY 1 GET 1'
- scrollUntilVisible:
      element:
          id: 'hotelClick'
      direction: DOWN
      timeout: 8000
      speed: 20
      centerElement: true
- scrollUntilVisible:
      element:
          id: 'hotelClick'
      direction: UP
      timeout: 8000
      speed: 30
      centerElement: true
- tapOn:
      id: 'backClick'      