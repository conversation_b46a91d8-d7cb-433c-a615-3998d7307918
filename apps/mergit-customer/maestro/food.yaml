appId: 'com.mergit.customer'
---

- assertVisible: 'Food'
- assertVisible:
      id: 'foodEntry'
- tapOn:
      id: 'foodEntry'
- assertVisible:
      id: 'searchClick'
- tapOn:
      id: 'searchClick'
- inputText: 'Biriyani'
- tapOn: 'search'
- assertVisible:
      id: 'search_result_food_0'
- tapOn:
      id: 'search_result_food_0'
- assertVisible:
      text: 'Dishes'
- tapOn: 'Dishes'
- assertVisible:
      text: 'Shops'
- tapOn: 'Shops'
- assertVisible:
      id: 'sortPress'
- tapOn:
      id: 'sortPress'
- tapOn: 'Price (High to Low)'
- tapOn: 'Price (Low to High)'
- tapOn: 'Distance (high to low)'
- tapOn:
      id: 'cancelModal'
- tapOn: 'Pure Veg'
- tapOn: 'Rated 4+'
- tapOn:
      id: 'modalBack'
- tapOn:
      id: 'modalBack'    