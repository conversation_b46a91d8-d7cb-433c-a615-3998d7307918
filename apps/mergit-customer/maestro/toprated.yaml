appId: 'com.mergit.customer'
---

- scrollUntilVisible:
      element:
          id: 'scroll_offerzone'
      direction: DOWN
      timeout: 8000
      speed: 20
      centerElement: true
- assertVisible:
      id: 'topRatedCard'
- tapOn:
      id: 'topRatedCard'
- assertVisible:
      text: 'TOP RATED'
- assertVisible:
      id: 'filterPress'
- tapOn:
      id: 'filterPress'
- tapOn:
      id: 'filterSearch'
- inputText: 'Indian'
- eraseText
- tapOn: 'go'
- tapOn:
      text: 'Indian'
- tapOn:
      text: 'Italian'
- tapOn:
      text: 'Thai'            
- assertVisible:
      text: 'Cuisines'
- assertVisible:
      text: 'Ratings'
- tapOn:
      text: 'Ratings'
- assertVisible:
      text: 'Veg / Non Veg'
- tapOn:
      text: 'Veg / Non Veg'
- tapOn:
      text: 'Non-Veg'
- tapOn:
      text: 'Pure Veg'      
- tapOn:
      text: 'Veg & Egg'    
- tapOn:
      text: 'Sort'
- tapOn:
      text: 'Price (High to Low)'  
- tapOn:
      text: 'Price (Low to High)'
- tapOn:
      text: 'Distance (high to low)' 
- tapOn:
      text: 'Apply'
- tapOn:
      id: 'closeModal'
- assertVisible:
      text: 'Pure Veg'
- assertVisible:
      text: 'Rated 4+'
- tapOn:
      text: 'Pure Veg'
- tapOn:
      text: 'Rated 4+'
- scrollUntilVisible:
      element:
          id: 'hotelClick'
      direction: DOWN
      timeout: 8000
      speed: 20
      centerElement: true
- scrollUntilVisible:
      element:
          id: 'hotelClick'
      direction: UP
      timeout: 8000
      speed: 30
      centerElement: true
- tapOn:
      id: 'backClick'      
- tapOn:
      text: 'Home'       