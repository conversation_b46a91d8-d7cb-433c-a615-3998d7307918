appId: "com.mergit.customer"
---

- assertVisible: 
    id: "add_new_address" 
- tapOn: 
    id: "add_new_address"
- assertVisible: 
    id: "location_confirm"  
- tapOn: 
    id: "location_confirm"
- assertVisible: 
    id: "complete_address"
- tapOn:
    id: "houseNo"
- inputText: "29"
- tapOn: "done"
- tapOn:
    id: "blockNo"
- inputText: "2"
- tapOn: "done"
- tapOn:
    id: "area_name"
- inputText: "Periyar Nagar"
- tapOn: "done"   
- assertVisible:
    id: "add_address_label"
- tapOn: "Home"    
- tapOn:
    id: "save_us"
- inputText: "<PERSON>" 
- tapOn: "done"   
- assertVisible:
    id: "receiver_details" 
- tapOn:
    id: "receiverName"  
- inputText: "<PERSON>"
- tapOn: "done"
- tapOn:
    id: "receiverNumber"
- inputText: "6382006743"
- tapOn: "done"
- tapOn:
    id: "continueButton"                                          